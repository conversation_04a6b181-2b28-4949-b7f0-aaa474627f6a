{"name": "checkin-app", "version": "0.1.0", "private": true, "packageManager": "pnpm@9.15.5", "engines": {"node": "22.x", "pnpm": ">=9"}, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate --config drizzle.config.ts", "db:migrate": "drizzle-kit migrate --config drizzle.config.ts", "db:studio": "drizzle-kit studio --config drizzle.config.ts"}, "dependencies": {"@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.54.0", "@tailwindcss/postcss": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "^0.44.4", "framer-motion": "^11.18.2", "lucide-react": "^0.468.0", "next": "15.4.6", "postcss": "^8.5.6", "react": "19.1.1", "react-dom": "19.1.1", "sonner": "^2.0.7", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.11", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/pg": "^8.15.5", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "eslint": "^9", "eslint-config-next": "15.4.6", "pg": "^8.16.3", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}