{"id": "111356d3-dccd-4ce1-9c3f-27fb5d06a7f0", "prevId": "5a1119de-fe16-4efa-9ef1-96aabbd28ecd", "version": "7", "dialect": "postgresql", "tables": {"public.checkins": {"name": "checkins", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "photo_url": {"name": "photo_url", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "device_info": {"name": "device_info", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.follows": {"name": "follows", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "follower_id": {"name": "follower_id", "type": "uuid", "primaryKey": false, "notNull": true}, "following_id": {"name": "following_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"follows_follower_id_following_id_unique": {"name": "follows_follower_id_following_id_unique", "nullsNotDistinct": false, "columns": ["follower_id", "following_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.profiles": {"name": "profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "display_name": {"name": "display_name", "type": "text", "primaryKey": false, "notNull": false}, "background_url": {"name": "background_url", "type": "text", "primaryKey": false, "notNull": false}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}